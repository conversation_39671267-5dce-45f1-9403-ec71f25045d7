# AWS API Gateway Setup Script for ReplyPal (PowerShell)
# This script creates and configures API Gateway to work with the replypal-api Lambda function

param(
    [string]$FunctionName = "replypal-api",
    [string]$ApiName = "replypal-api", 
    [string]$StageName = "prod",
    [string]$Region = "us-east-1"
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Setting up API Gateway for ReplyPal..." -ForegroundColor Green
Write-Host "Function: $FunctionName" -ForegroundColor Cyan
Write-Host "API Name: $ApiName" -ForegroundColor Cyan
Write-Host "Region: $Region" -ForegroundColor Cyan
Write-Host "Stage: $StageName" -ForegroundColor Cyan
Write-Host ""

# Check if Lambda function exists
Write-Host "📋 Checking if Lambda function exists..." -ForegroundColor Yellow
try {
    aws lambda get-function --function-name $FunctionName --region $Region --output json | Out-Null
    Write-Host "✅ Lambda function found" -ForegroundColor Green
} catch {
    Write-Host "❌ Error: Lambda function '$FunctionName' not found in region '$Region'" -ForegroundColor Red
    Write-Host "Please deploy your Lambda function first using the deployment guide." -ForegroundColor Red
    exit 1
}

# Create API Gateway
Write-Host "🔧 Creating API Gateway..." -ForegroundColor Yellow
$ApiId = aws apigateway create-rest-api `
    --name $ApiName `
    --description "ReplyPal API Gateway for Chrome Extension" `
    --endpoint-configuration types=REGIONAL `
    --region $Region `
    --query 'id' --output text

if (-not $ApiId) {
    Write-Host "❌ Error: Failed to create API Gateway" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Created API Gateway with ID: $ApiId" -ForegroundColor Green

# Get root resource ID
Write-Host "🔧 Getting root resource..." -ForegroundColor Yellow
$RootResourceId = aws apigateway get-resources `
    --rest-api-id $ApiId `
    --region $Region `
    --query 'items[0].id' --output text

Write-Host "✅ Root resource ID: $RootResourceId" -ForegroundColor Green

# Create proxy resource
Write-Host "🔧 Creating proxy resource..." -ForegroundColor Yellow
$ProxyResourceId = aws apigateway create-resource `
    --rest-api-id $ApiId `
    --parent-id $RootResourceId `
    --path-part '{proxy+}' `
    --region $Region `
    --query 'id' --output text

Write-Host "✅ Created proxy resource with ID: $ProxyResourceId" -ForegroundColor Green

# Create ANY method for proxy resource
Write-Host "🔧 Creating ANY method..." -ForegroundColor Yellow
aws apigateway put-method `
    --rest-api-id $ApiId `
    --resource-id $ProxyResourceId `
    --http-method ANY `
    --authorization-type NONE `
    --region $Region | Out-Null

Write-Host "✅ Created ANY method" -ForegroundColor Green

# Get Lambda function ARN
Write-Host "🔧 Getting Lambda function ARN..." -ForegroundColor Yellow
$LambdaArn = aws lambda get-function `
    --function-name $FunctionName `
    --region $Region `
    --query 'Configuration.FunctionArn' --output text

Write-Host "✅ Lambda ARN: $LambdaArn" -ForegroundColor Green

# Set up Lambda integration
Write-Host "🔧 Setting up Lambda integration..." -ForegroundColor Yellow
aws apigateway put-integration `
    --rest-api-id $ApiId `
    --resource-id $ProxyResourceId `
    --http-method ANY `
    --type AWS_PROXY `
    --integration-http-method POST `
    --uri "arn:aws:apigateway:${Region}:lambda:path/2015-03-31/functions/$LambdaArn/invocations" `
    --region $Region | Out-Null

Write-Host "✅ Lambda integration configured" -ForegroundColor Green

# Add Lambda permission for API Gateway
Write-Host "🔧 Adding Lambda permissions..." -ForegroundColor Yellow
$Timestamp = [int][double]::Parse((Get-Date -UFormat %s))
aws lambda add-permission `
    --function-name $FunctionName `
    --statement-id "apigateway-invoke-$Timestamp" `
    --action lambda:InvokeFunction `
    --principal apigateway.amazonaws.com `
    --source-arn "arn:aws:execute-api:${Region}:*:${ApiId}/*/*" `
    --region $Region | Out-Null

Write-Host "✅ Lambda permissions added" -ForegroundColor Green

# Deploy API
Write-Host "🔧 Deploying API..." -ForegroundColor Yellow
aws apigateway create-deployment `
    --rest-api-id $ApiId `
    --stage-name $StageName `
    --region $Region | Out-Null

Write-Host "✅ API deployed to stage: $StageName" -ForegroundColor Green

# Get API URL
$ApiUrl = "https://$ApiId.execute-api.$Region.amazonaws.com/$StageName"

Write-Host ""
Write-Host "🎉 API Gateway setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "  API Gateway ID: $ApiId" -ForegroundColor White
Write-Host "  API URL: $ApiUrl" -ForegroundColor White
Write-Host "  Stage: $StageName" -ForegroundColor White
Write-Host "  Region: $Region" -ForegroundColor White
Write-Host ""
Write-Host "🧪 Test your API:" -ForegroundColor Cyan
Write-Host "  Health check: curl $ApiUrl/ping" -ForegroundColor White
Write-Host "  Generate endpoint: curl -X POST $ApiUrl/generate" -ForegroundColor White
Write-Host ""
Write-Host "📝 Next steps:" -ForegroundColor Cyan
Write-Host "  1. Update your Chrome extension settings to use: $ApiUrl" -ForegroundColor White
Write-Host "  2. Test the API endpoints" -ForegroundColor White
Write-Host "  3. Configure custom domain (optional)" -ForegroundColor White
Write-Host ""
