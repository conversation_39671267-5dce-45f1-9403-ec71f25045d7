#!/bin/bash

# AWS API Gateway Setup Script for ReplyPal
# This script creates and configures API Gateway to work with the replypal-api Lambda function

set -e  # Exit on any error

# Configuration
FUNCTION_NAME="replypal-api"
API_NAME="replypal-api"
STAGE_NAME="prod"
REGION="us-east-1"  # Change this to your preferred region

echo "🚀 Setting up API Gateway for ReplyPal..."
echo "Function: $FUNCTION_NAME"
echo "API Name: $API_NAME"
echo "Region: $REGION"
echo "Stage: $STAGE_NAME"
echo ""

# Check if Lambda function exists
echo "📋 Checking if Lambda function exists..."
if ! aws lambda get-function --function-name $FUNCTION_NAME --region $REGION >/dev/null 2>&1; then
    echo "❌ Error: Lambda function '$FUNCTION_NAME' not found in region '$REGION'"
    echo "Please deploy your Lambda function first using the deployment guide."
    exit 1
fi
echo "✅ Lambda function found"

# Create API Gateway
echo "🔧 Creating API Gateway..."
API_ID=$(aws apigateway create-rest-api \
  --name $API_NAME \
  --description "ReplyPal API Gateway for Chrome Extension" \
  --endpoint-configuration types=REGIONAL \
  --region $REGION \
  --query 'id' --output text)

if [ -z "$API_ID" ]; then
    echo "❌ Error: Failed to create API Gateway"
    exit 1
fi

echo "✅ Created API Gateway with ID: $API_ID"

# Get root resource ID
echo "🔧 Getting root resource..."
ROOT_RESOURCE_ID=$(aws apigateway get-resources \
  --rest-api-id $API_ID \
  --region $REGION \
  --query 'items[0].id' --output text)

echo "✅ Root resource ID: $ROOT_RESOURCE_ID"

# Create proxy resource
echo "🔧 Creating proxy resource..."
PROXY_RESOURCE_ID=$(aws apigateway create-resource \
  --rest-api-id $API_ID \
  --parent-id $ROOT_RESOURCE_ID \
  --path-part '{proxy+}' \
  --region $REGION \
  --query 'id' --output text)

echo "✅ Created proxy resource with ID: $PROXY_RESOURCE_ID"

# Create ANY method for proxy resource
echo "🔧 Creating ANY method..."
aws apigateway put-method \
  --rest-api-id $API_ID \
  --resource-id $PROXY_RESOURCE_ID \
  --http-method ANY \
  --authorization-type NONE \
  --region $REGION

echo "✅ Created ANY method"

# Get Lambda function ARN
echo "🔧 Getting Lambda function ARN..."
LAMBDA_ARN=$(aws lambda get-function \
  --function-name $FUNCTION_NAME \
  --region $REGION \
  --query 'Configuration.FunctionArn' --output text)

echo "✅ Lambda ARN: $LAMBDA_ARN"

# Set up Lambda integration
echo "🔧 Setting up Lambda integration..."
aws apigateway put-integration \
  --rest-api-id $API_ID \
  --resource-id $PROXY_RESOURCE_ID \
  --http-method ANY \
  --type AWS_PROXY \
  --integration-http-method POST \
  --uri "arn:aws:apigateway:$REGION:lambda:path/2015-03-31/functions/$LAMBDA_ARN/invocations" \
  --region $REGION

echo "✅ Lambda integration configured"

# Add Lambda permission for API Gateway
echo "🔧 Adding Lambda permissions..."
aws lambda add-permission \
  --function-name $FUNCTION_NAME \
  --statement-id apigateway-invoke-$(date +%s) \
  --action lambda:InvokeFunction \
  --principal apigateway.amazonaws.com \
  --source-arn "arn:aws:execute-api:$REGION:*:$API_ID/*/*" \
  --region $REGION

echo "✅ Lambda permissions added"

# Deploy API
echo "🔧 Deploying API..."
aws apigateway create-deployment \
  --rest-api-id $API_ID \
  --stage-name $STAGE_NAME \
  --region $REGION

echo "✅ API deployed to stage: $STAGE_NAME"

# Get API URL
API_URL="https://$API_ID.execute-api.$REGION.amazonaws.com/$STAGE_NAME"

echo ""
echo "🎉 API Gateway setup completed successfully!"
echo ""
echo "📋 Summary:"
echo "  API Gateway ID: $API_ID"
echo "  API URL: $API_URL"
echo "  Stage: $STAGE_NAME"
echo "  Region: $REGION"
echo ""
echo "🧪 Test your API:"
echo "  Health check: curl $API_URL/ping"
echo "  Generate endpoint: curl -X POST $API_URL/generate"
echo ""
echo "📝 Next steps:"
echo "  1. Update your Chrome extension settings to use: $API_URL"
echo "  2. Test the API endpoints"
echo "  3. Configure custom domain (optional)"
echo ""
