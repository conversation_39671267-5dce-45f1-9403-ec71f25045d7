#!/usr/bin/env python3
"""
<PERSON><PERSON>t to create a deployment package for AWS Lambda from the ReplyPal FastAPI application.
This script creates a ZIP file containing all necessary files for Lambda deployment.
"""

import os
import shutil
import zipfile
import tempfile
from pathlib import Path
import sys

def should_exclude_file(file_path, filename):
    """
    Determine if a file should be excluded from the Lambda package.
    """
    exclude_patterns = [
        # Cache files
        '__pycache__',
        '.pyc',
        '.pyo',
        '.pyd',
        
        # Windows executables
        '.exe',
        '.cmd',
        '.bat',
        
        # Development files
        '.git',
        '.pytest_cache',
        'tests',
        'test_',
        
        # Documentation
        'README',
        'LICENSE',
        'CHANGELOG',
        'docs',
        
        # Virtual environment
        'env',
        'venv',
        '.env',
        
        # IDE files
        '.vscode',
        '.idea',
        
        # OS files
        '.DS_Store',
        'Thumbs.db',
        
        # Large unnecessary files
        'examples',
        'benchmarks',
        'tutorials',
    ]
    
    # Check if any exclude pattern matches
    for pattern in exclude_patterns:
        if pattern in filename.lower() or pattern in str(file_path).lower():
            return True
    
    # Exclude specific large directories that aren't needed
    exclude_dirs = [
        'boto3/examples',
        'boto3/docs',
        'botocore/docs',
        'openai/cli',
        'stripe/test_helpers',
        'tests',
        'test',
    ]
    
    for exclude_dir in exclude_dirs:
        if exclude_dir in str(file_path):
            return True
    
    return False

def copy_app_code(temp_dir):
    """
    Copy the application code to the temporary directory.
    """
    print("Copying application code...")
    
    # Copy the main entry point
    shutil.copy2('main.py', temp_dir)
    
    # Copy the app directory
    app_src = Path('app')
    app_dst = Path(temp_dir) / 'app'
    
    def copy_tree(src, dst):
        """Recursively copy a directory tree, excluding unwanted files."""
        dst.mkdir(exist_ok=True)
        
        for item in src.iterdir():
            if should_exclude_file(item, item.name):
                continue
                
            if item.is_file():
                shutil.copy2(item, dst / item.name)
            elif item.is_dir():
                copy_tree(item, dst / item.name)
    
    copy_tree(app_src, app_dst)
    print(f"✓ Copied application code to {app_dst}")

def copy_dependencies(temp_dir):
    """
    Copy necessary dependencies from the Package directory.
    """
    print("Copying dependencies...")
    
    package_dir = Path('Package')
    if not package_dir.exists():
        print("Warning: Package directory not found. Dependencies may not be included.")
        return
    
    copied_count = 0
    skipped_count = 0
    
    for item in package_dir.iterdir():
        if should_exclude_file(item, item.name):
            skipped_count += 1
            continue
        
        dst_path = Path(temp_dir) / item.name
        
        try:
            if item.is_file():
                shutil.copy2(item, dst_path)
                copied_count += 1
            elif item.is_dir():
                shutil.copytree(item, dst_path, ignore=lambda dir, files: [
                    f for f in files if should_exclude_file(Path(dir) / f, f)
                ])
                copied_count += 1
        except Exception as e:
            print(f"Warning: Could not copy {item.name}: {e}")
            skipped_count += 1
    
    print(f"✓ Copied {copied_count} dependencies, skipped {skipped_count}")

def create_lambda_handler(temp_dir):
    """
    Create a Lambda handler file that uses Mangum to wrap the FastAPI app.
    """
    print("Creating Lambda handler...")
    
    handler_content = '''"""
AWS Lambda handler for ReplyPal FastAPI application.
This file provides the Lambda entry point using Mangum.
"""

from mangum import Mangum
from app.main import app

# Create the Lambda handler
handler = Mangum(app, lifespan="off")

# For backwards compatibility, also export as lambda_handler
lambda_handler = handler
'''
    
    handler_path = Path(temp_dir) / 'lambda_handler.py'
    with open(handler_path, 'w') as f:
        f.write(handler_content)
    
    print(f"✓ Created Lambda handler at {handler_path}")

def create_zip_file(temp_dir, output_path):
    """
    Create a ZIP file from the temporary directory.
    """
    print(f"Creating ZIP file: {output_path}")
    
    with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(temp_dir):
            # Skip directories that should be excluded
            dirs[:] = [d for d in dirs if not should_exclude_file(Path(root) / d, d)]
            
            for file in files:
                if should_exclude_file(Path(root) / file, file):
                    continue
                
                file_path = Path(root) / file
                arcname = file_path.relative_to(temp_dir)
                zipf.write(file_path, arcname)
    
    # Get file size
    size_mb = os.path.getsize(output_path) / (1024 * 1024)
    print(f"✓ Created ZIP file: {output_path} ({size_mb:.1f} MB)")
    
    if size_mb > 50:
        print("⚠️  Warning: ZIP file is larger than 50MB. Consider optimizing dependencies.")
    
    return size_mb

def main():
    """
    Main function to build the Lambda deployment package.
    """
    print("=" * 60)
    print("Building AWS Lambda deployment package for ReplyPal")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path('app').exists() or not Path('main.py').exists():
        print("Error: This script must be run from the fastapi directory")
        print("Expected files: app/ directory and main.py")
        sys.exit(1)
    
    # Create output filename
    output_filename = 'replypal_lambda.zip'
    
    # Remove existing ZIP file if it exists
    if Path(output_filename).exists():
        print(f"Removing existing {output_filename}")
        os.remove(output_filename)
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Using temporary directory: {temp_dir}")
        
        try:
            # Copy application code
            copy_app_code(temp_dir)
            
            # Copy dependencies
            copy_dependencies(temp_dir)
            
            # Create Lambda handler
            create_lambda_handler(temp_dir)
            
            # Create ZIP file
            size_mb = create_zip_file(temp_dir, output_filename)
            
            print("\n" + "=" * 60)
            print("✅ Lambda deployment package created successfully!")
            print(f"📦 File: {output_filename}")
            print(f"📏 Size: {size_mb:.1f} MB")
            print("\nNext steps:")
            print("1. Upload this ZIP file to AWS Lambda")
            print("2. Set the handler to: lambda_handler.handler")
            print("3. Configure environment variables in Lambda")
            print("4. Set up API Gateway integration")
            print("=" * 60)
            
        except Exception as e:
            print(f"\n❌ Error creating Lambda package: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
